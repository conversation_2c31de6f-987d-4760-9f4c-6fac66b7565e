import os
import re
import openpyxl
import json
import time
from datetime import datetime, timedelta
from dotenv import load_dotenv
from trag import TRAG
from trag.types.document import Document
from backend.app.utils.tapd import tap_client
load_dotenv()


class TRAGManager:
    """TRAG 文档管理类，负责文档的导入、搜索和清理"""

    def __init__(self, ns_code=os.getenv("TRAG_NAMESPACE"), coll_code=os.getenv("TRAG_CASE_COLL"), trag_token=os.getenv("TRAG_TOKEN")):
        """初始化 TRAG 连接和 TAPD 工具"""
        self.rag = TRAG.from_api_key(api_key=trag_token)
        self.namespace = self.rag.namespace(ns_code)
        self.collection = self.namespace.collection(coll_code)
        print(self.rag.info)

    def import_test_cases(self, file_path):
        """
        从 Excel 导入测试用例到 TRAG
        :param file_path: Excel 文件路径
        """
        headers = [
            "用例ID", "UUID", "用例标题", "用例目录", "用例描述", "用例负责人",
            "用例类型", "是否自动化", "等级", "前置条件", "步骤描述类型",
            "步骤描述", "预期结果", "评估工时", "标签", "关联需求",
            "关联的自动化用例", "执行次数", "创建人", "创建时间", "测试框架"
        ]
        workbook = openpyxl.load_workbook(file_path)
        sheet = workbook.active
        documents = []

        for row in sheet.iter_rows(min_row=2, values_only=True):
            row_data = self._parse_excel_row(row, headers)
            document = self._generate_case_document(row_data)
            documents.append(document)
            print(document.embedding_query)  # 打印简化版用例信息
            print(document.doc)  # 打印完整 JSON 格式

        workbook.close()
        self.collection.import_documents(documents)
        print(f"成功导入 {len(documents)} 个测试用例")

    def import_require(self, urls):
        """
        从 TAPD URL 列表导入需求文档到 TRAG
        :param urls: TAPD 需求 URL 列表
        """
        documents = []
        for url in urls:
            require = tap_client.get_story(url)
            if require:
                document = self._generate_require_document(require)
                documents.append(document)
                print(document.embedding_query)  # 打印简化版需求信息
        self.collection.import_documents(documents)
        print(f"成功导入 {len(documents)} 个需求")

    def import_bugs_from_dataset(self, workspace_ids=None, batch_size=50):
        """
        从数据库导入缺陷数据到TRAG

        Args:
            workspace_ids: 工作空间ID列表，如果为None则获取所有工作空间的数据
            batch_size: 批处理大小，默认50个
        """
        from sqlalchemy.orm import Session
        from sqlalchemy import distinct
        from backend.app.database.database import get_db
        from backend.app.models.bug import BugEvaluation
        import time

        try:
            # 获取数据库连接
            db_gen = get_db()
            db: Session = next(db_gen)

            try:
                # 构建查询，获取所有不重复的bug_id和workspace_id组合
                query = db.query(
                    BugEvaluation.workspace_id,
                    BugEvaluation.bug_id
                ).distinct()

                # 如果指定了工作空间ID，则过滤
                if workspace_ids:
                    if isinstance(workspace_ids, str):
                        workspace_ids = [workspace_ids]
                    query = query.filter(BugEvaluation.workspace_id.in_(workspace_ids))

                results = query.all()

                if not results:
                    print("数据库中没有找到BUG数据")
                    return

                # 按工作空间分组
                bugs_by_workspace = {}
                for workspace_id, bug_id in results:
                    if workspace_id not in bugs_by_workspace:
                        bugs_by_workspace[workspace_id] = []
                    bugs_by_workspace[workspace_id].append(bug_id)

                total_bugs = sum(len(bugs) for bugs in bugs_by_workspace.values())
                print(f"从数据库获取到 {len(bugs_by_workspace)} 个工作空间的 {total_bugs} 个BUG")

                # 处理每个工作空间的BUG
                all_documents = []
                processed_count = 0
                failed_count = 0

                for workspace_id, bug_ids in bugs_by_workspace.items():
                    print(f"\n开始处理工作空间 {workspace_id} 的 {len(bug_ids)} 个BUG")

                    # 分批处理BUG
                    for i in range(0, len(bug_ids), batch_size):
                        batch_bug_ids = bug_ids[i:i + batch_size]
                        batch_documents = []

                        print(f"处理批次 {i//batch_size + 1}: {len(batch_bug_ids)} 个BUG")

                        for bug_id in batch_bug_ids:
                            try:
                                # 调用TAPD API获取完整BUG数据
                                bug_data = tap_client.get_bug_all_pure_message(workspace_id, bug_id)

                                if bug_data:
                                    # 生成文档对象
                                    document = self._generate_bug_document_enhanced(bug_data, workspace_id)
                                    batch_documents.append(document)
                                    processed_count += 1

                                    if processed_count % 10 == 0:
                                        print(f"已处理 {processed_count}/{total_bugs} 个BUG")
                                else:
                                    print(f"获取BUG数据失败: {workspace_id}/{bug_id}")
                                    failed_count += 1

                            except Exception as e:
                                print(f"处理BUG失败 {workspace_id}/{bug_id}: {str(e)}")
                                failed_count += 1
                                continue

                            # 添加延迟避免API限制
                            time.sleep(0.1)

                        # 批量导入到TRAG
                        if batch_documents:
                            try:
                                self.collection.import_documents(batch_documents)
                                all_documents.extend(batch_documents)
                                print(f"成功导入批次 {len(batch_documents)} 个文档到TRAG")
                            except Exception as e:
                                print(f"导入批次文档到TRAG失败: {str(e)}")
                                failed_count += len(batch_documents)

                print(f"\n=== 导入完成 ===")
                print(f"总计处理: {total_bugs} 个BUG")
                print(f"成功导入: {len(all_documents)} 个文档")
                print(f"处理失败: {failed_count} 个")
                print(f"成功率: {len(all_documents)/total_bugs*100:.2f}%")

            finally:
                db.close()

        except Exception as e:
            print(f"导入BUG数据失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def import_bugs_from_past_year(self, workspace_ids=None, batch_size=50):
        """
        获取近一年的bug数据，对每个调用process_bug_data处理，然后存入向量数据库

        Args:
            workspace_ids: 工作空间ID列表，如果为None则使用默认工作空间
            batch_size: 批处理大小，默认50个
        """
        try:
            # 计算近一年的时间范围
            end_time = datetime.now()
            start_time = end_time - timedelta(days=365)

            start_time_str = start_time.strftime("%Y-%m-%d 00:00:00")
            end_time_str = end_time.strftime("%Y-%m-%d %H:%M:%S")

            print(f"开始获取近一年的BUG数据 ({start_time_str} ~ {end_time_str})")

            # 如果没有指定工作空间ID，使用默认的工作空间ID
            if workspace_ids is None:
                # 可以从环境变量或配置中获取默认工作空间ID
                default_workspace = os.getenv("DEFAULT_WORKSPACE_ID", "20375472")
                workspace_ids = [default_workspace]
            elif isinstance(workspace_ids, str):
                workspace_ids = [workspace_ids]

            all_documents = []
            total_processed = 0
            total_failed = 0

            for workspace_id in workspace_ids:
                print(f"\n开始处理工作空间: {workspace_id}")

                # 使用get_all_bugs_by_time_range获取指定时间范围内的所有bug
                bugs_data = tap_client.get_all_bugs_by_time_range(
                    workspace_id=workspace_id,
                    start_time=start_time_str,
                    end_time=end_time_str
                )

                print(f"工作空间 {workspace_id} 获取到 {len(bugs_data)} 个BUG")

                if not bugs_data:
                    print(f"工作空间 {workspace_id} 没有找到BUG数据")
                    continue

                # 分批处理BUG
                for i in range(0, len(bugs_data), batch_size):
                    batch_bugs = bugs_data[i:i + batch_size]
                    batch_documents = []

                    print(f"处理批次 {i//batch_size + 1}: {len(batch_bugs)} 个BUG")

                    for bug_item in batch_bugs:
                        try:
                            # 提取bug数据
                            raw_bug = bug_item.get("Bug", {})
                            bug_id = raw_bug.get("id")

                            if not bug_id:
                                print(f"跳过无效的BUG数据: {bug_item}")
                                continue

                            # 调用process_bug_data处理bug数据
                            processed_bug_data = tap_client.process_bug_data(raw_bug, workspace_id)

                            if processed_bug_data:
                                # 生成文档对象并添加到批次中
                                document = self._generate_bug_document_enhanced(processed_bug_data, workspace_id)
                                batch_documents.append(document)
                                total_processed += 1

                                if total_processed % 10 == 0:
                                    print(f"已处理 {total_processed} 个BUG")
                            else:
                                print(f"处理BUG数据失败: {workspace_id}/{bug_id}")
                                total_failed += 1

                        except Exception as e:
                            print(f"处理BUG失败 {workspace_id}/{bug_id}: {str(e)}")
                            total_failed += 1
                            continue

                        # 添加延迟避免API限制
                        time.sleep(0.1)

                    # 批量导入到TRAG
                    if batch_documents:
                        try:
                            self.collection.import_documents(batch_documents)
                            all_documents.extend(batch_documents)
                            print(f"成功导入批次 {len(batch_documents)} 个文档到TRAG")
                        except Exception as e:
                            print(f"导入批次文档到TRAG失败: {str(e)}")
                            total_failed += len(batch_documents)

            print(f"\n=== 近一年BUG数据导入完成 ===")
            print(f"时间范围: {start_time_str} ~ {end_time_str}")
            print(f"处理工作空间: {workspace_ids}")
            print(f"成功导入: {len(all_documents)} 个文档")
            print(f"处理失败: {total_failed} 个")
            if total_processed + total_failed > 0:
                print(f"成功率: {len(all_documents)/(total_processed + total_failed)*100:.2f}%")

            return {
                "success": True,
                "total_imported": len(all_documents),
                "total_failed": total_failed,
                "time_range": f"{start_time_str} ~ {end_time_str}",
                "workspace_ids": workspace_ids
            }

        except Exception as e:
            print(f"导入近一年BUG数据失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "error": str(e)
            }

    def import_bugs(self, directory_path="bugs"):
        """
        从指定目录下的所有Excel文件导入BUG单到TRAG
        :param directory_path: Excel文件所在目录路径
        """
        import glob
        import os

        # 获取目录下所有的xlsx文件
        excel_files = glob.glob(os.path.join(directory_path, "*.xlsx"))
        
        total_documents = []
        
        for file_path in excel_files:
            print(f"正在处理文件: {file_path}")
            workbook = openpyxl.load_workbook(file_path)
            sheet = workbook.active
            
            # 从第一行获取动态headers
            headers = [cell.value for cell in sheet[1] if cell.value is not None]
            
            documents = []
            for row in sheet.iter_rows(min_row=2, values_only=False):
                row_data = self._parse_excel_row_solve_link(row, headers)
                if row_data:  # 确保行数据不为空
                    document = self._generate_bug_document(row_data)
                    documents.append(document)
                    print(document.embedding_query)  # 打印简化版用例信息
                    print(document.doc)  # 打印完整 JSON 格式

            workbook.close()
            total_documents.extend(documents)
            print(f"从文件 {file_path} 成功导入 {len(documents)} 个缺陷")
        
        if total_documents:
            self.collection.import_documents(total_documents)
            print(f"总共成功导入 {len(total_documents)} 个缺陷")
        else:
            print("没有找到任何缺陷数据")

    def search_test_cases(self, query, limit=10):
        """
        搜索测试用例
        :param query: 搜索查询字符串
        :param limit: 返回结果数量限制
        :return: 搜索结果列表
        """
        return self.collection.search_documents(doc=query, limit=limit)

    def search_bugs(self, query, limit=10):
        """
        搜索缺陷
        :param query: 搜索查询字符串
        :param limit: 返回结果数量限制
        :return: 搜索结果列表
        """
        return self.collection.search_documents(doc=query, limit=limit)
    def clean_documents(self):
        """清空 TRAG 文档库"""
        self.collection.clean_documents()

    def _parse_excel_row(self, row, headers):
        """
        解析 Excel 单行数据
        :param row: Excel 行数据
        :param headers: 表头列表
        :return: 解析后的字典
        """
        return {header: row[i] if row[i] is not None else "" for i, header in enumerate(headers)}


    def _parse_excel_row_solve_link(self, row, headers) -> dict:
        """
        解析Excel 单行数据
        :param row: Excel 行数据
        :param headers: 表头列表
        :return: 解析后的字典
        """
        result = {}
        for i, header in enumerate(headers):
            if row[i].value is not None:
                if row[i].hyperlink:
                    result[header] = row[i].value
                    result[header + "链接"] = row[i].hyperlink.target
                else:
                    result[header] = row[i].value
        return result

    def _generate_case_document(self, row_data):
        """
        生成测试用例文档对象
        :param row_data: 用例数据字典
        :return: Document 对象
        """
        simple_case = (
            f"用例目录: {row_data['用例目录']}\n"
            f"用例标题: {row_data['用例标题']}\n"
            f"步骤描述: {row_data['步骤描述']}\n"
            f"预期结果: {row_data['预期结果']}\n"
        )
        original_case = json.dumps(row_data, ensure_ascii=False, indent=2)
        return Document(
            id=row_data['用例ID'],
            embedding_query=simple_case,
            doc=original_case
        )

    def _generate_require_document(self, require):
        """
        生成需求文档对象
        :param require: 需求数据字典
        :return: Document 对象
        """
        require_str = json.dumps(require, ensure_ascii=False, indent=2)
        return Document(
            id=require['id'],
            embedding_query=require_str,
            doc=require_str
        )

    def _generate_bug_document(self, row_data):
        """
        生成缺陷文档对象
        :param row_data: 缺陷数据字典
        :return: Document 对象
        """
        simple_case = (
            f"缺陷标题：{row_data['缺陷标题']}\n"
            f"模块：{row_data.get('模块', '')}\n"
            f"关联需求：{row_data.get('关联需求','')}\n"
            f"状态：{row_data['状态']}\n"
            f"缺陷引入来源：{row_data.get('缺陷引入来源', '')}\n"
            f"优先级：{row_data.get('优先级', '')}\n"
            f"重现规律：{row_data.get('重现规律', '')}\n"
        )
        original_case = json.dumps(row_data, ensure_ascii=False, indent=2)
        return Document(
            id=row_data['缺陷ID'],
            embedding_query=simple_case,
            doc=original_case
        )
    def _generate_bug_document_enhanced(self, bug_data, workspace_id):
        """
        生成增强版缺陷文档对象，用于从数据库导入的BUG数据

        Args:
            bug_data: 从TAPD API获取的完整BUG数据
            workspace_id: 工作空间ID

        Returns:
            Document 对象
        """
        # 提取关键信息用于 embedding 查询
        title = bug_data.get('缺陷标题') or bug_data.get('标题', '')
        description = bug_data.get('详细描述') or bug_data.get('描述', '')
        module = bug_data.get('模块', '')
        status = bug_data.get('状态', '')
        priority = bug_data.get('优先级', '')
        severity = bug_data.get('严重程度', '')
        creator = bug_data.get('创建人', '')
        bug_id = bug_data.get('ID') or bug_data.get('缺陷ID', '')
        create_time = bug_data.get('创建时间', '')
        embedding_query = f"模块:\n ====={module}=====\n标题:\n ====={title}=====\n描述:\n====={description}"

        # 构建中文字段名的文档内容（给大模型看的 key）
        doc_content = {
            '标题': title,
            '描述': description,
            '模块': module,
            '状态': status,
            '优先级': priority,
            '严重程度': severity,
            '创建人': creator,
            '创建时间': create_time
        }
        # 文档 ID 仍然使用英文拼接，保持唯一性
        document_id = f"{workspace_id}_{bug_id}"
        return Document(
            id=document_id,
            embedding_query=embedding_query,
            doc=json.dumps(doc_content, ensure_ascii=False, indent=2)
        )
   
def create_ns(name):
    """创建命名空间"""
    rag = TRAG.from_api_key(api_key=os.getenv("TRAG_TOKEN"))
    return rag.create_namespace(name, description="")


def create_col(ns, name, description="", embedding_model="public-bge-m3", dimension=1024):
    """创建知识库集合"""
    return ns.create_collection(name=name, description=description, embedding_model=embedding_model, dimension=dimension)


if __name__ == "__main__":
    # 用例知识库全流程
    # 选择知识库
    manager = TRAGManager(coll_code=os.getenv("TRAG_CASE_COLL"))

    # 新增功能：导入近一年的BUG数据
    # 可以指定工作空间ID，如果不指定则使用默认工作空间
    # result = manager.import_bugs_from_past_year(workspace_ids=["20375472"], batch_size=30)
    # print(f"导入结果: {result}")

    # # 导入
    # manager.import_test_cases("./data/test_cases.xlsx")
    #manager.import_bugs("../../data/bugs_896_优先级低.xlsx")
    # 检索
    #case_search = manager.search_test_cases(query="关于AIGC深度思考模块场景验证的历史用例有哪些", limit=10)
    #print(case_search)
    bugs_search = manager.search_bugs("健康管理助手模块有哪些历史BUG", limit=10)
    print(bugs_search)
    # # 需求知识库全流程
    # manager = TRAGManager(coll_code=os.getenv("TRAG_STORY_COLL"))
    # urls = [
    #     "https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472123851503?from_iteration_id=1020375472002148335",
    #     "https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472122077437?from_iteration_id=1020375472002148335",
    #     "https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472122596893?from_iteration_id=1020375472002148335",
    #     "https://tapd.woa.com/tapd_fe/20375472/story/detail/1020375472122425098?from_iteration_id=1020375472002148335",
    # ]
    # manager.import_require(urls)
    # require_search = manager.search_test_cases(query="关于咨询人的需求", limit=1)
    # print(require_search)